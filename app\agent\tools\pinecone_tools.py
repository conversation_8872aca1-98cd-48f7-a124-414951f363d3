"""
Pinecone search tools for AI agents.
"""

import logging
from typing import Dict, Any, List

from app.agent.tools.base import <PERSON>Tool, ToolSchema, ToolResult, tool_registry
from app.core.config import settings
from app.embedding.service import EmbeddingService
from pinecone import Pinecone

logger = logging.getLogger(__name__)


class PineconeSearchTool(BaseTool):
    """Tool for searching a Pinecone index."""

    def __init__(self):
        super().__init__()
        self.pc = Pinecone(api_key=settings.PINECONE_API_KEY)
        self.index_name = settings.PINECONE_INDEX_NAME
        self.index = self.pc.Index(self.index_name)
        self.embedding_service = EmbeddingService()

    @property
    def name(self) -> str:
        return "pinecone_search"

    @property
    def description(self) -> str:
        return "Search for data in a Pinecone index using a text query"

    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "query_text": {
                        "type": "string",
                        "description": "The text to search for",
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "The number of results to return",
                        "default": 5,
                    },
                    "namespace": {
                        "type": "string",
                        "description": "The namespace to search in",
                        "default": None,
                    }
                },
                "required": ["query_text"],
            },
        )

    async def execute(self, **kwargs) -> ToolResult:
        """
        Perform a search in the Pinecone index.

        Args:
            query_text: The text to search for.
            top_k: The number of results to return.
            namespace: The namespace to search in.

        Returns:
            ToolResult with search results.
        """
        try:
            query_text = kwargs.get("query_text")
            top_k = kwargs.get("top_k", 5)
            namespace = kwargs.get("namespace")

            if not query_text:
                return ToolResult(success=False, error="Query text is required")

            # Get the embedding for the query text
            query_vector = await self.embedding_service.embed_text(query_text)

            results = self.index.query(
                vector=query_vector,
                top_k=top_k,
                include_metadata=True,
                namespace=namespace
            )

            return ToolResult(
                success=True,
                result=results.to_dict(),
            )

        except Exception as e:
            logger.error(f"Pinecone search error: {str(e)}")
            return ToolResult(success=False, error=f"Pinecone search failed: {str(e)}")


# Register tools
tool_registry.register_tool(PineconeSearchTool())
