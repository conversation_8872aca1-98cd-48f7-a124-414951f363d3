import os
import asyncio
from dotenv import load_dotenv
from app.embedding.service import EmbeddingService
from pinecone import Pinecone

# Load environment variables from .env file
load_dotenv()

# Get Pinecone credentials from environment variables
PINECONE_API_KEY = 'pcsk_7N93Dy_QqZY6KapREgei6bqWmrMYb9R97WvmWe1GfZHvLbnbtSppQGV9fUSjbb9pRWbirZ   '
PINECONE_INDEX_NAME = 'n8nxapadatadev'

async def test_pinecone_text_query():
    """
    Tests the connection to Pinecone and performs a simple text query.
    """
    if not all([PINECONE_API_KEY, PINECONE_INDEX_NAME]):
        print("Error: Pinecone environment variables are not set.")
        print("Please ensure PINECONE_API_KEY and PINECONE_INDEX_NAME are in your .env file.")
        return

    try:
        # Initialize Pinecone
        pc = Pinecone(api_key=PINECONE_API_KEY)
        print("Successfully initialized Pinecone.")

        # Get the index
        index = pc.Index(PINECONE_INDEX_NAME)
        print(f"Successfully connected to index: {PINECONE_INDEX_NAME}")

        # Get index stats to check if it's empty
        stats = index.describe_index_stats()
        print(f"Index stats: {stats}")

        if stats['total_vector_count'] == 0:
            print("\nWarning: The index is empty, so no query will be performed.")
            print("Please add data to your index to test querying.")
            return

        # Initialize the embedding service
        embedding_service = EmbeddingService()
        print("Initialized embedding service.")

        # The text to search for
        query_text = "What is the capital of France?"
        print(f"\nPerforming a query with the text: '{query_text}'")

        # Get the embedding for the query text
        query_vector = await embedding_service.embed_text(query_text)
        print("Successfully created embedding for the query text.")

        # Perform a query
        query_results = index.query(
            vector=query_vector,
            top_k=3,
            include_metadata=True
        )

        print("\nQuery successful!")
        print("Results:")
        print(query_results)

    except Exception as e:
        print(f"\nAn error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(test_pinecone_text_query())

