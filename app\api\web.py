from fastapi import APIRouter, Request, Depends, HTTPException, status
from fastapi.responses import HTMLResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.orm import Session
from typing import Optional

from app.models.base import get_db
from app.models.role import Role

router = APIRouter()
templates = Jinja2Templates(directory="templates")


@router.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page."""
    return templates.TemplateResponse("home.html", {"request": request})


@router.get("/roles", response_class=HTMLResponse)
async def roles_list(request: Request):
    """Roles management page."""
    return templates.TemplateResponse("roles/list.html", {"request": request})


@router.get("/roles/create", response_class=HTMLResponse)
async def create_role_page(request: Request):
    """Create role page."""
    return templates.TemplateResponse("roles/form.html", {
        "request": request,
        "role": None
    })


@router.get("/roles/{role_name}/edit", response_class=HTMLResponse)
async def edit_role_page(
    request: Request,
    role_name: str,
    db: Session = Depends(get_db)
):
    """Edit role page."""
    role = db.query(Role).filter(Role.name == role_name).first()

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_name}' not found"
        )
    
    # Convert role to dict for template
    role_dict = {
        "id": str(role.id),
        "name": role.name,
        "display_name": role.display_name,
        "description": role.description,
        "system_prompt": role.system_prompt,
        "tools": role.tools or [],
        "config": role.config or {},
        "is_active": role.is_active,
        "created_at": role.created_at.isoformat() if role.created_at else None,
        "updated_at": role.updated_at.isoformat() if role.updated_at else None
    }
    
    return templates.TemplateResponse("roles/form.html", {
        "request": request,
        "role": role_dict
    })


@router.get("/tools", response_class=HTMLResponse)
async def tools_page(request: Request):
    """Tools management page."""
    return templates.TemplateResponse("tools/list.html", {"request": request})


@router.get("/test", response_class=HTMLResponse)
async def test_agent_page(
    request: Request,
    role: Optional[str] = None
):
    """AI agent testing page."""
    return templates.TemplateResponse("test/agent.html", {
        "request": request,
        "selected_role": role
    })


@router.get("/health", response_class=HTMLResponse)
async def health_page(request: Request):
    """System health page."""
    return templates.TemplateResponse("admin/health.html", {"request": request})
