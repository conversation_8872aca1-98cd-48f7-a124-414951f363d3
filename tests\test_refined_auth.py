#!/usr/bin/env python3
"""
Test script for the refined authentication system.
Tests the new authentication endpoints: /verify and /anonymous
"""

import requests
import json
import sys
from typing import Optional

BASE_URL = "http://localhost:8000"


class AuthTester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """Test health endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")
            return False
    
    def test_anonymous_auth(self) -> Optional[str]:
        """Test anonymous authentication."""
        try:
            response = self.session.post(f"{self.base_url}/api/v1/auth/anonymous")
            
            if response.status_code == 200:
                token_data = response.json()
                token = token_data["access_token"]
                print("✅ Anonymous authentication successful")
                print(f"   Token type: {token_data.get('token_type', 'N/A')}")
                print(f"   Expires in: {token_data.get('expires_in', 'N/A')} seconds")
                return token
            else:
                print(f"❌ Anonymous authentication failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Anonymous authentication error: {str(e)}")
            return None
    
    def test_verify_token(self, token: str) -> bool:
        """Test token verification."""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(f"{self.base_url}/api/v1/auth/verify", headers=headers)
            
            if response.status_code == 200:
                user_data = response.json()
                print("✅ Token verification successful")
                print(f"   User ID: {user_data.get('id', 'N/A')}")
                print(f"   Username: {user_data.get('username', 'N/A')}")
                print(f"   Email: {user_data.get('email', 'N/A')}")
                print(f"   Is Active: {user_data.get('is_active', 'N/A')}")
                return True
            else:
                print(f"❌ Token verification failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Token verification error: {str(e)}")
            return False
    
    def test_protected_endpoint(self, token: str) -> bool:
        """Test accessing a protected endpoint with the token."""
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = self.session.get(f"{self.base_url}/api/v1/sessions", headers=headers)
            
            if response.status_code == 200:
                sessions_data = response.json()
                print("✅ Protected endpoint access successful")
                print(f"   Sessions count: {len(sessions_data.get('sessions', []))}")
                return True
            else:
                print(f"❌ Protected endpoint access failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Protected endpoint access error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all authentication tests."""
        print("🧪 Testing Refined Authentication System\n")
        
        # Test basic connectivity
        if not self.test_health():
            print("❌ Basic connectivity failed. Is the server running?")
            return False
        
        # Test anonymous authentication
        print("\n📋 Testing Anonymous Authentication")
        token = self.test_anonymous_auth()
        if not token:
            return False
        
        # Test token verification
        print("\n📋 Testing Token Verification")
        if not self.test_verify_token(token):
            return False
        
        # Test protected endpoint access
        print("\n📋 Testing Protected Endpoint Access")
        if not self.test_protected_endpoint(token):
            return False
        
        print("\n🎉 All authentication tests passed!")
        return True


def main():
    """Main function."""
    base_url = sys.argv[1] if len(sys.argv) > 1 else BASE_URL
    
    tester = AuthTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
