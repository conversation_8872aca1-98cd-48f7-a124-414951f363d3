"""
Code execution and analysis tools for AI agents.

This module contains tools for executing code and analyzing programming tasks.
"""

import logging
import subprocess
import tempfile
import os
from typing import Dict, Any, List

from app.agent.tools.base import BaseTool, ToolSchema, ToolResult, tool_registry

logger = logging.getLogger(__name__)


class CodeExecutorTool(BaseTool):
    """Tool for executing code in various programming languages."""
    
    @property
    def name(self) -> str:
        return "code_executor"
    
    @property
    def description(self) -> str:
        return "Execute code snippets in various programming languages"
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "code": {
                        "type": "string",
                        "description": "Code to execute"
                    },
                    "language": {
                        "type": "string",
                        "description": "Programming language",
                        "enum": ["python", "javascript", "bash", "shell"],
                        "default": "python"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Execution timeout in seconds",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 30
                    }
                },
                "required": ["code"]
            }
        )
    
    async def execute(self, **kwargs) -> ToolResult:
        """
        Execute code in a sandboxed environment.
        
        Args:
            code: Code to execute
            language: Programming language
            timeout: Execution timeout
            
        Returns:
            ToolResult with execution results
        """
        try:
            code = kwargs.get("code", "").strip()
            language = kwargs.get("language", "python")
            timeout = kwargs.get("timeout", 10)
            
            if not code:
                return ToolResult(
                    success=False,
                    error="No code provided"
                )
            
            # Security check - basic validation
            if self._is_unsafe_code(code):
                return ToolResult(
                    success=False,
                    error="Code contains potentially unsafe operations"
                )
            
            # Execute code based on language
            if language == "python":
                result = await self._execute_python(code, timeout)
            elif language in ["javascript", "js"]:
                result = await self._execute_javascript(code, timeout)
            elif language in ["bash", "shell"]:
                result = await self._execute_shell(code, timeout)
            else:
                return ToolResult(
                    success=False,
                    error=f"Unsupported language: {language}"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Code execution error: {str(e)}")
            return ToolResult(
                success=False,
                error=f"Code execution failed: {str(e)}"
            )
    
    def _is_unsafe_code(self, code: str) -> bool:
        """Basic security check for unsafe operations."""
        unsafe_patterns = [
            "import os",
            "import sys",
            "import subprocess",
            "exec(",
            "eval(",
            "__import__",
            "open(",
            "file(",
            "input(",
            "raw_input(",
            "rm -",
            "del ",
            "format(",
            "system(",
        ]
        
        code_lower = code.lower()
        return any(pattern in code_lower for pattern in unsafe_patterns)
    
    async def _execute_python(self, code: str, timeout: int) -> ToolResult:
        """Execute Python code safely."""
        try:
            # Create a temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Execute the Python code
                result = subprocess.run(
                    ["python", temp_file],
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                
                return ToolResult(
                    success=result.returncode == 0,
                    result={
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "return_code": result.returncode
                    },
                    metadata={
                        "language": "python",
                        "execution_time": timeout
                    }
                )
            finally:
                # Clean up temporary file
                os.unlink(temp_file)
                
        except subprocess.TimeoutExpired:
            return ToolResult(
                success=False,
                error=f"Code execution timed out after {timeout} seconds"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Python execution error: {str(e)}"
            )
    
    async def _execute_javascript(self, code: str, timeout: int) -> ToolResult:
        """Execute JavaScript code using Node.js."""
        try:
            # Create a temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(code)
                temp_file = f.name
            
            try:
                # Execute the JavaScript code
                result = subprocess.run(
                    ["node", temp_file],
                    capture_output=True,
                    text=True,
                    timeout=timeout
                )
                
                return ToolResult(
                    success=result.returncode == 0,
                    result={
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "return_code": result.returncode
                    },
                    metadata={
                        "language": "javascript",
                        "execution_time": timeout
                    }
                )
            finally:
                # Clean up temporary file
                os.unlink(temp_file)
                
        except subprocess.TimeoutExpired:
            return ToolResult(
                success=False,
                error=f"Code execution timed out after {timeout} seconds"
            )
        except FileNotFoundError:
            return ToolResult(
                success=False,
                error="Node.js not found. Please install Node.js to execute JavaScript code."
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"JavaScript execution error: {str(e)}"
            )
    
    async def _execute_shell(self, code: str, timeout: int) -> ToolResult:
        """Execute shell commands (limited for security)."""
        try:
            # Very restrictive shell execution
            allowed_commands = ["echo", "ls", "pwd", "date", "whoami"]
            first_word = code.split()[0] if code.split() else ""
            
            if first_word not in allowed_commands:
                return ToolResult(
                    success=False,
                    error=f"Shell command '{first_word}' not allowed for security reasons"
                )
            
            result = subprocess.run(
                code,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            return ToolResult(
                success=result.returncode == 0,
                result={
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "return_code": result.returncode
                },
                metadata={
                    "language": "shell",
                    "execution_time": timeout
                }
            )
            
        except subprocess.TimeoutExpired:
            return ToolResult(
                success=False,
                error=f"Shell execution timed out after {timeout} seconds"
            )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Shell execution error: {str(e)}"
            )


# Register tools
tool_registry.register_tool(CodeExecutorTool())
