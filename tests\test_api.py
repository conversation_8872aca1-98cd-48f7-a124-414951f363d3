#!/usr/bin/env python3
"""
Simple test script for the AI Agent System API.
Tests Phase 1 (Authentication) and Phase 2 (Session Management) functionality.
"""

import requests
import json
import sys
from typing import Optional

BASE_URL = "http://localhost:8000"


class APITester:
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.token: Optional[str] = None
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """Test health endpoint."""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {str(e)}")
            return False
    
    def test_anonymous_auth(self) -> bool:
        """Test anonymous authentication."""
        try:
            response = self.session.post(f"{self.base_url}/api/v1/auth/anonymous")

            if response.status_code == 200:
                token_data = response.json()
                self.token = token_data["access_token"]
                self.session.headers.update({"Authorization": f"Bearer {self.token}"})
                print("✅ Anonymous authentication successful")
                return True
            else:
                print(f"❌ Anonymous authentication failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Anonymous authentication error: {str(e)}")
            return False

    def test_verify_token(self) -> bool:
        """Test token verification."""
        if not self.token:
            print("❌ No token available for verification")
            return False

        try:
            response = self.session.get(f"{self.base_url}/api/v1/auth/verify")

            if response.status_code == 200:
                user_data = response.json()
                print(f"✅ Token verification successful - User: {user_data.get('username', 'N/A')}")
                return True
            else:
                print(f"❌ Token verification failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Token verification error: {str(e)}")
            return False
    
    def test_list_roles(self) -> bool:
        """Test listing available roles."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sessions/roles/")
            
            if response.status_code == 200:
                roles = response.json()
                print(f"✅ Found {len(roles['roles'])} available roles:")
                for role in roles["roles"]:
                    print(f"   - {role['name']}: {role['display_name']}")
                return True
            else:
                print(f"❌ List roles failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ List roles error: {str(e)}")
            return False
    
    def test_create_session(self, name: str = "Test Session", role: str = "assistant") -> Optional[str]:
        """Test creating a session."""
        try:
            data = {
                "name": name,
                "role_name": role
            }
            response = self.session.post(f"{self.base_url}/api/v1/sessions/", json=data)
            
            if response.status_code == 201:
                session_data = response.json()
                session_key = session_data["session_key"]
                print(f"✅ Session created successfully: {session_key}")
                return session_key
            else:
                print(f"❌ Session creation failed: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            print(f"❌ Session creation error: {str(e)}")
            return None
    
    def test_list_sessions(self) -> bool:
        """Test listing user sessions."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sessions/")
            
            if response.status_code == 200:
                sessions_data = response.json()
                print(f"✅ Found {sessions_data['total']} user sessions")
                for session in sessions_data["sessions"]:
                    print(f"   - {session['name']} ({session['role_name']}) - {session['session_key']}")
                return True
            else:
                print(f"❌ List sessions failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ List sessions error: {str(e)}")
            return False
    
    def test_get_session(self, session_key: str) -> bool:
        """Test getting session details."""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sessions/{session_key}")
            
            if response.status_code == 200:
                session_data = response.json()
                print(f"✅ Session details retrieved: {session_data['name']}")
                return True
            else:
                print(f"❌ Get session failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Get session error: {str(e)}")
            return False
    
    def test_update_session(self, session_key: str, new_name: str = "Updated Test Session") -> bool:
        """Test updating session name."""
        try:
            data = {"name": new_name}
            response = self.session.put(f"{self.base_url}/api/v1/sessions/{session_key}", json=data)
            
            if response.status_code == 200:
                session_data = response.json()
                print(f"✅ Session updated: {session_data['name']}")
                return True
            else:
                print(f"❌ Update session failed: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Update session error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all API tests."""
        print("🧪 Starting API Tests for Phase 1 & 2\n")
        
        # Test basic connectivity
        if not self.test_health():
            print("❌ Basic connectivity failed. Is the server running?")
            return False
        
        # Test authentication (Phase 1)
        print("\n📋 Testing Phase 1: Authentication")
        if not self.test_anonymous_auth():
            return False

        if not self.test_verify_token():
            return False
        
        # Test session management (Phase 2)
        print("\n📋 Testing Phase 2: Session Management")
        if not self.test_list_roles():
            return False
        
        session_key = self.test_create_session()
        if not session_key:
            return False
        
        if not self.test_list_sessions():
            return False
        
        if not self.test_get_session(session_key):
            return False
        
        if not self.test_update_session(session_key):
            return False
        
        print("\n✅ All tests passed! Phase 1 & 2 are working correctly.")
        print("\n📝 Next steps:")
        print("   - Implement Phase 3: LLM Integration")
        print("   - Implement Phase 4: WebSocket Communication")
        print("   - Implement Phase 5: Tools & Testing")
        
        return True


if __name__ == "__main__":
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = BASE_URL
    
    tester = APITester(base_url)
    success = tester.run_all_tests()
    
    if not success:
        sys.exit(1)